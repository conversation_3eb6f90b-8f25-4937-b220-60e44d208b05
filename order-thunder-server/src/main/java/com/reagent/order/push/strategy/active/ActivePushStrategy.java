package com.reagent.order.push.strategy.active;

import com.reagent.bid.api.rpc.dto.BidPreInfoDTO;
import com.reagent.bid.api.rpc.dto.BidPreOrderDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.config.docking.DockingConfigManager;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.push.active.PushToTPIServiceFactory;
import com.reagent.order.push.active.service.PushToTPIService;
import com.reagent.order.push.active.service.PushToTpiParamDTO;
import com.reagent.order.push.active.service.impl.order.PushOrderToTPIServiceImpl;
import com.reagent.order.push.strategy.active.annotations.DockingStrategy;
import com.reagent.order.push.strategy.active.annotations.PushEvent;
import com.reagent.order.rpc.OrderDetailRPCClient;
import com.reagent.order.rpc.OrderMasterCommonRPCClient;
import com.reagent.order.rpc.bid.BidClient;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/28 11:08
 * @description
 * 与策略已然无关，用于与OrderPushEventEnum一对一关系，用于转换事件与推送模型
 */
@Service
public abstract class ActivePushStrategy {

    @Resource
    protected DockingConfigManager dockingConfigManager;

    @Resource
    private OrderDetailRPCClient orderDetailRpcClient;

    @Resource
    private OrderMasterCommonRPCClient orderMasterCommonRpcClient;

    @Resource
    private BidClient bidClient;

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void start(ActiveStrategyParam activeStrategyParam) {
        if(activeStrategyParam.getPhase() == null){
            // 定义阶段，非复杂单位不使用phase即可。直接定义为phase1
            activeStrategyParam.setPhase(1);
        }
        if (true) {
            return;
        }
        this.executeStrategy(activeStrategyParam);
    }

    /**
     * 实际执行策略处理
     * @param activeStrategyParam 参数
     */
    protected void executeStrategy(ActiveStrategyParam activeStrategyParam) {
        if(dockingConfigManager.getConfigDTO(activeStrategyParam.getOrgCode()).getEnable()){
            this.execute(activeStrategyParam);
        }else {
            this.executeInOldMode(activeStrategyParam);
        }
    }

    protected void execute(ActiveStrategyParam activeStrategyParam){
        // 1.校验是否有配置是否符合执行要求
        if(!configValidate(activeStrategyParam)){
            return;
        }
        // 2.校验品类是否符合执行要求
        OrgDockingConfigDTO config = dockingConfigManager.getConfigDTO(activeStrategyParam.getOrgCode());
        List<Integer> relatedCtgIdList = config.getBaseConfig().getRelatedCategory();
        if(CollectionUtils.isNotEmpty(relatedCtgIdList)){
            Integer orderId = activeStrategyParam.getOrderId();
            if(orderId == null && activeStrategyParam.getOrderNo() != null){
                // 有订单号没有订单id的，就要获取
                OrderMasterDTO orderMasterDTO = orderMasterCommonRpcClient.findByOrderNo(activeStrategyParam.getOrderNo());
                if(orderMasterDTO == null){
                    // 没有匹配的订单就返回
                    return;
                }
                orderId = orderMasterDTO.getId();
            }
            if(orderId != null){
                // 判断是否所有品类都符合配置，有不符合的就不执行
                List<OrderDetailDTO> orderDetailDTOList = orderDetailRpcClient.findOrderDetailByMasterId(New.list(orderId));
                if(!Optional.ofNullable(orderDetailDTOList).orElseGet(New::emptyList).stream().allMatch(orderDetailDTO -> relatedCtgIdList.contains(orderDetailDTO.getFirstCategoryId())
                        || relatedCtgIdList.contains(orderDetailDTO.getSecondCategoryId()) || relatedCtgIdList.contains(orderDetailDTO.getCategoryid()))){
                    return;
                }
            }else if(activeStrategyParam.getBidId() != null){
                BidPreInfoDTO bidPreInfoDTO = bidClient.findBidPreOrderInfoListByBidId(activeStrategyParam.getBidId());
                if(bidPreInfoDTO == null){
                    return;
                }
                if(!bidPreInfoDTO.getBidPreOrderDTOList().stream().map(BidPreOrderDTO::getBidPreOrderDetailDTOList).flatMap(List::stream).allMatch(orderDetailDTO -> relatedCtgIdList.contains(orderDetailDTO.getFirstCategoryId())
                        || relatedCtgIdList.contains(orderDetailDTO.getSecondCategoryId()) || relatedCtgIdList.contains(orderDetailDTO.getCategoryid()))){
                    return;
                }
            }
        }
        // 3.获取推送数据模型
        Class<? extends PushToTPIService> pushModel = this.getCustomPushModel(activeStrategyParam);
        PushEvent annotation = this.getClass().getAnnotation(PushEvent.class);
        if(annotation == null){
            // 没注解的，还没启用配置，不推送
            return;
        }
        pushModel = pushModel != null ? pushModel : annotation.defaultExecuteClazz();
        // 4.执行
        PushToTPIService pushToTpiService = PushToTPIServiceFactory.getPushToTpiServiceByOrgCodeAndClass(activeStrategyParam.getOrgCode(), pushModel);
        PushToTpiParamDTO pushToTpiParamDTO = new PushToTpiParamDTO();
        pushToTpiParamDTO.setOrgCode(activeStrategyParam.getOrgCode());
        pushToTpiParamDTO.setOrderNo(activeStrategyParam.getOrderNo());
        pushToTpiParamDTO.setReturnId(activeStrategyParam.getReturnId());
        pushToTpiParamDTO.setBidId(activeStrategyParam.getBidId());
        pushToTpiParamDTO.setEventType(annotation.eventType());
        pushToTpiParamDTO.setPhase(activeStrategyParam.getPhase());
        pushToTpiService.push(pushToTpiParamDTO);
    }

    protected boolean configValidate(ActiveStrategyParam activeStrategyParam){
        return this.configValidate(activeStrategyParam.getOrgCode());
    }

    /**
     * 校验是否配置启用
     */
    protected boolean configValidate(String orgCode){
        return false;
    }

    /**
     * 机构代码与推送数据模型映射获取，有定制化的才有
     */
    protected Class<? extends PushToTPIService> getCustomPushModel(ActiveStrategyParam activeStrategyParam){
        if(OrgEnum.NAN_FANG_YI_KE.getCode().equals(activeStrategyParam.getOrgCode())){
            return PushOrderToTPIServiceImpl.class;
        }
        return null;
    }

    /**
     * 旧的，DockingStrategy那套
     */
    @Deprecated
    protected void executeInOldMode(ActiveStrategyParam activeStrategyParam){
        DockingStrategy annotation = this.getClass().getAnnotation(DockingStrategy.class);
        // 南方医科做特殊处理，推送订单
        Class<? extends PushToTPIService> clazz = OrgEnum.NAN_FANG_YI_KE.getCode().equals(activeStrategyParam.getOrgCode()) ? PushOrderToTPIServiceImpl.class : annotation.executeClazz();
        PushToTPIService pushToTpiService = PushToTPIServiceFactory.getPushToTpiServiceByOrgCodeAndClass(activeStrategyParam.getOrgCode(), clazz);
        PushToTpiParamDTO pushToTpiParamDTO = new PushToTpiParamDTO();
        pushToTpiParamDTO.setOrgCode(activeStrategyParam.getOrgCode());
        pushToTpiParamDTO.setOrderNo(activeStrategyParam.getOrderNo());
        pushToTpiParamDTO.setReturnId(activeStrategyParam.getReturnId());
        pushToTpiParamDTO.setBidId(activeStrategyParam.getBidId());
        pushToTpiParamDTO.setEventType(annotation.eventType());
        pushToTpiService.push(pushToTpiParamDTO);
    }
}
